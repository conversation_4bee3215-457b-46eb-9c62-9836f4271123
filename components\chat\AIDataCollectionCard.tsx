import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/i18n/useTranslation';
import { DataCollectionState } from '@/services/ai-data-collector';
import { 
  Database, 
  CheckCircle, 
  Clock,
  AlertCircle
} from 'lucide-react-native';

interface AIDataCollectionCardProps {
  state: DataCollectionState;
  onConfirmSave: () => void;
  onCancel: () => void;
  style?: any;
}

export const AIDataCollectionCard: React.FC<AIDataCollectionCardProps> = ({
  state,
  onConfirmSave,
  onCancel,
  style,
}) => {
  const { t, isRTL } = useTranslation();

  const getEntityIcon = () => {
    const iconProps = { size: 20, color: colors.primary };
    switch (state.entityType) {
      case 'plant':
        return '🌱';
      case 'animal':
        return '🐄';
      case 'garden':
        return '🌻';
      case 'field':
        return '🌾';
      case 'equipment':
        return '🚜';
      case 'task':
        return '📋';
      default:
        return '📝';
    }
  };

  const getProgressPercentage = () => {
    const totalRequired = Object.keys(state.collectedData).length;
    const collected = Object.values(state.collectedData).filter(v => v !== null && v !== '').length;
    return totalRequired > 0 ? Math.round((collected / totalRequired) * 100) : 0;
  };

  if (!state.isComplete) {
    // Show collection in progress
    return (
      <View style={[styles.card, styles.progressCard, style, isRTL && styles.cardRtl]}>
        <View style={[styles.header, isRTL && styles.headerRtl]}>
          <View style={styles.iconContainer}>
            <Text style={styles.entityIcon}>{getEntityIcon()}</Text>
          </View>
          <View style={styles.headerText}>
            <Text style={[styles.title, isRTL && styles.textRtl]}>
              Collecting {state.entityType} data...
            </Text>
            <Text style={[styles.subtitle, isRTL && styles.textRtl]}>
              {getProgressPercentage()}% complete
            </Text>
          </View>
          <Clock size={16} color={colors.warning} />
        </View>

        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill, 
                { width: `${getProgressPercentage()}%` }
              ]} 
            />
          </View>
        </View>

        <Text style={[styles.instruction, isRTL && styles.textRtl]}>
          Please answer the questions above to complete the {state.entityType} information.
        </Text>
      </View>
    );
  }

  // Show completion summary
  return (
    <View style={[styles.card, styles.completionCard, style, isRTL && styles.cardRtl]}>
      <View style={[styles.header, isRTL && styles.headerRtl]}>
        <View style={styles.iconContainer}>
          <Text style={styles.entityIcon}>{getEntityIcon()}</Text>
        </View>
        <View style={styles.headerText}>
          <Text style={[styles.title, isRTL && styles.textRtl]}>
            {state.entityType.charAt(0).toUpperCase() + state.entityType.slice(1)} Data Complete
          </Text>
          <Text style={[styles.subtitle, isRTL && styles.textRtl]}>
            Ready to save
          </Text>
        </View>
        <CheckCircle size={20} color={colors.success} />
      </View>

      <View style={styles.dataPreview}>
        <Text style={[styles.dataTitle, isRTL && styles.textRtl]}>
          Collected Information:
        </Text>
        {Object.entries(state.collectedData).map(([key, value]) => (
          <View key={key} style={[styles.dataRow, isRTL && styles.dataRowRtl]}>
            <Text style={[styles.dataKey, isRTL && styles.textRtl]}>
              {key.charAt(0).toUpperCase() + key.slice(1)}:
            </Text>
            <Text style={[styles.dataValue, isRTL && styles.textRtl]}>
              {String(value)}
            </Text>
          </View>
        ))}
      </View>

      <View style={[styles.actions, isRTL && styles.actionsRtl]}>
        <TouchableOpacity
          style={[styles.cancelButton, isRTL && styles.buttonRtl]}
          onPress={onCancel}
        >
          <Text style={[styles.cancelButtonText, isRTL && styles.textRtl]}>
            Cancel
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.saveButton, isRTL && styles.buttonRtl]}
          onPress={onConfirmSave}
        >
          <Database size={16} color={colors.white} />
          <Text style={[styles.saveButtonText, isRTL && styles.textRtl]}>
            Save {state.entityType}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardRtl: {
    // RTL specific styles
  },
  progressCard: {
    borderLeftWidth: 4,
    borderLeftColor: colors.warning,
  },
  completionCard: {
    borderLeftWidth: 4,
    borderLeftColor: colors.success,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  headerRtl: {
    flexDirection: 'row-reverse',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  entityIcon: {
    fontSize: 20,
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 12,
    color: colors.gray[600],
  },
  textRtl: {
    textAlign: 'right',
  },
  progressContainer: {
    marginBottom: 12,
  },
  progressBar: {
    height: 4,
    backgroundColor: colors.gray[200],
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.warning,
    borderRadius: 2,
  },
  instruction: {
    fontSize: 14,
    color: colors.gray[600],
    fontStyle: 'italic',
  },
  dataPreview: {
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  dataTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 8,
  },
  dataRow: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  dataRowRtl: {
    flexDirection: 'row-reverse',
  },
  dataKey: {
    fontSize: 13,
    fontWeight: '500',
    color: colors.gray[700],
    minWidth: 80,
    marginRight: 8,
  },
  dataValue: {
    fontSize: 13,
    color: colors.gray[600],
    flex: 1,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  actionsRtl: {
    flexDirection: 'row-reverse',
  },
  cancelButton: {
    flex: 1,
    backgroundColor: colors.gray[200],
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  saveButton: {
    flex: 2,
    backgroundColor: colors.success,
    borderRadius: 8,
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
  },
  buttonRtl: {
    // RTL specific button styles
  },
  cancelButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[700],
  },
  saveButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.white,
  },
});
