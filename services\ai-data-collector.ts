export interface EntityField {
  name: string;
  label: string;
  type: 'text' | 'number' | 'date' | 'select' | 'boolean';
  required: boolean;
  options?: string[]; // For select type
  validation?: (value: any) => boolean;
  placeholder?: string;
}

export interface EntitySchema {
  type: 'plant' | 'animal' | 'garden' | 'field' | 'equipment' | 'task';
  fields: EntityField[];
}

export interface DataCollectionState {
  entityType: string;
  collectedData: { [key: string]: any };
  currentFieldIndex: number;
  isComplete: boolean;
  missingFields: string[];
}

export class AIDataCollector {
  private static entitySchemas: { [key: string]: EntitySchema } = {
    plant: {
      type: 'plant',
      fields: [
        {
          name: 'name',
          label: 'Plant Name',
          type: 'text',
          required: true,
          placeholder: 'e.g., Tomato Plant #1'
        },
        {
          name: 'species',
          label: 'Species',
          type: 'text',
          required: true,
          placeholder: 'e.g., Solanum lycopersicum'
        },
        {
          name: 'variety',
          label: 'Variety',
          type: 'text',
          required: false,
          placeholder: 'e.g., Roma, Cherry, Bee<PERSON>teak'
        },
        {
          name: 'gardenId',
          label: 'Garden Location',
          type: 'select',
          required: false,
          placeholder: 'Select garden (optional)'
        }
      ]
    },
    animal: {
      type: 'animal',
      fields: [
        {
          name: 'name',
          label: 'Animal Name',
          type: 'text',
          required: true,
          placeholder: 'e.g., Bessie, Cow #123'
        },
        {
          name: 'species',
          label: 'Species',
          type: 'text',
          required: true,
          placeholder: 'e.g., Bos taurus (Cattle)'
        },
        {
          name: 'breed',
          label: 'Breed',
          type: 'text',
          required: false,
          placeholder: 'e.g., Holstein, Angus'
        },
        {
          name: 'dateOfBirth',
          label: 'Date of Birth',
          type: 'date',
          required: false,
          placeholder: 'YYYY-MM-DD'
        }
      ]
    },
    garden: {
      type: 'garden',
      fields: [
        {
          name: 'name',
          label: 'Garden Name',
          type: 'text',
          required: true,
          placeholder: 'e.g., Vegetable Garden, Rose Garden'
        },
        {
          name: 'type',
          label: 'Garden Type',
          type: 'select',
          required: true,
          options: ['vegetable', 'flower', 'herb', 'fruit', 'mixed'],
          placeholder: 'Select garden type'
        },
        {
          name: 'size',
          label: 'Size (square meters)',
          type: 'number',
          required: true,
          placeholder: 'e.g., 100'
        },
        {
          name: 'location',
          label: 'Location Description',
          type: 'text',
          required: true,
          placeholder: 'e.g., Behind the house, North field'
        }
      ]
    },
    field: {
      type: 'field',
      fields: [
        {
          name: 'name',
          label: 'Field Name',
          type: 'text',
          required: true,
          placeholder: 'e.g., North Field, Wheat Field #1'
        },
        {
          name: 'size',
          label: 'Size (hectares)',
          type: 'number',
          required: true,
          placeholder: 'e.g., 5.5'
        },
        {
          name: 'type',
          label: 'Field Type',
          type: 'select',
          required: true,
          options: ['cropland', 'pasture', 'orchard', 'fallow'],
          placeholder: 'Select field type'
        },
        {
          name: 'location',
          label: 'Location Description',
          type: 'text',
          required: true,
          placeholder: 'e.g., East side of farm'
        }
      ]
    },
    equipment: {
      type: 'equipment',
      fields: [
        {
          name: 'name',
          label: 'Equipment Name',
          type: 'text',
          required: true,
          placeholder: 'e.g., John Deere Tractor'
        },
        {
          name: 'type',
          label: 'Equipment Type',
          type: 'select',
          required: true,
          options: ['tractor', 'harvester', 'plow', 'seeder', 'sprayer', 'other'],
          placeholder: 'Select equipment type'
        },
        {
          name: 'manufacturer',
          label: 'Manufacturer',
          type: 'text',
          required: false,
          placeholder: 'e.g., John Deere, Case IH'
        },
        {
          name: 'model',
          label: 'Model',
          type: 'text',
          required: false,
          placeholder: 'e.g., 5075E, Magnum 340'
        }
      ]
    },
    task: {
      type: 'task',
      fields: [
        {
          name: 'title',
          label: 'Task Title',
          type: 'text',
          required: true,
          placeholder: 'e.g., Water tomato plants'
        },
        {
          name: 'description',
          label: 'Description',
          type: 'text',
          required: true,
          placeholder: 'Detailed description of the task'
        },
        {
          name: 'priority',
          label: 'Priority',
          type: 'select',
          required: true,
          options: ['low', 'medium', 'high', 'urgent'],
          placeholder: 'Select priority level'
        },
        {
          name: 'dueDate',
          label: 'Due Date',
          type: 'date',
          required: false,
          placeholder: 'YYYY-MM-DD'
        }
      ]
    }
  };

  static detectEntityType(message: string): string | null {
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('plant') || lowerMessage.includes('crop') || lowerMessage.includes('seed')) {
      return 'plant';
    }
    if (lowerMessage.includes('animal') || lowerMessage.includes('cow') || lowerMessage.includes('cattle') || 
        lowerMessage.includes('chicken') || lowerMessage.includes('pig') || lowerMessage.includes('sheep')) {
      return 'animal';
    }
    if (lowerMessage.includes('garden')) {
      return 'garden';
    }
    if (lowerMessage.includes('field')) {
      return 'field';
    }
    if (lowerMessage.includes('equipment') || lowerMessage.includes('tractor') || lowerMessage.includes('machine')) {
      return 'equipment';
    }
    if (lowerMessage.includes('task') || lowerMessage.includes('job') || lowerMessage.includes('work')) {
      return 'task';
    }
    
    return null;
  }

  static initializeDataCollection(entityType: string): DataCollectionState {
    return {
      entityType,
      collectedData: {},
      currentFieldIndex: 0,
      isComplete: false,
      missingFields: []
    };
  }

  static getNextQuestion(state: DataCollectionState): string | null {
    const schema = this.entitySchemas[state.entityType];
    if (!schema) return null;

    const requiredFields = schema.fields.filter(f => f.required);
    const currentField = requiredFields[state.currentFieldIndex];
    
    if (!currentField) {
      // Check if all required fields are collected
      const missingRequired = requiredFields.filter(f => 
        !state.collectedData.hasOwnProperty(f.name) || 
        state.collectedData[f.name] === null || 
        state.collectedData[f.name] === ''
      );
      
      if (missingRequired.length === 0) {
        state.isComplete = true;
        return null;
      } else {
        state.missingFields = missingRequired.map(f => f.name);
        return `I still need: ${missingRequired.map(f => f.label).join(', ')}. Let's continue with ${missingRequired[0].label}:`;
      }
    }

    let question = `What is the ${currentField.label}?`;
    
    if (currentField.type === 'select' && currentField.options) {
      question += ` Options: ${currentField.options.join(', ')}`;
    }
    
    if (currentField.placeholder) {
      question += ` (${currentField.placeholder})`;
    }

    return question;
  }

  static processAnswer(state: DataCollectionState, answer: string): boolean {
    const schema = this.entitySchemas[state.entityType];
    if (!schema) return false;

    const requiredFields = schema.fields.filter(f => f.required);
    const currentField = requiredFields[state.currentFieldIndex];
    
    if (!currentField) return false;

    // Process the answer based on field type
    let processedValue: any = answer.trim();
    
    switch (currentField.type) {
      case 'number':
        processedValue = parseFloat(answer);
        if (isNaN(processedValue)) {
          return false; // Invalid number
        }
        break;
      case 'date':
        // Basic date validation
        if (!/^\d{4}-\d{2}-\d{2}$/.test(answer)) {
          return false; // Invalid date format
        }
        break;
      case 'select':
        if (currentField.options && !currentField.options.includes(answer.toLowerCase())) {
          return false; // Invalid option
        }
        processedValue = answer.toLowerCase();
        break;
    }

    // Store the answer
    state.collectedData[currentField.name] = processedValue;
    state.currentFieldIndex++;

    return true;
  }

  static getCompletionSummary(state: DataCollectionState): string {
    const schema = this.entitySchemas[state.entityType];
    if (!schema) return '';

    const summary = Object.entries(state.collectedData)
      .map(([key, value]) => {
        const field = schema.fields.find(f => f.name === key);
        return `${field?.label || key}: ${value}`;
      })
      .join('\n');

    return `I have gathered the following data for your ${state.entityType}:\n\n${summary}\n\nShall I save this ${state.entityType} to your farm?`;
  }

  static getEntitySchema(entityType: string): EntitySchema | null {
    return this.entitySchemas[entityType] || null;
  }
}
