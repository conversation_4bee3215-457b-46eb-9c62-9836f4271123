import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { colors } from '@/constants/colors';
import { Lightbulb } from 'lucide-react-native';

interface MCPSummaryDisplayProps {
  summary: string;
  isRTL: boolean;
}

export default function MCPSummaryDisplay({ summary, isRTL }: MCPSummaryDisplayProps) {
  return (
    <View style={[styles.container, isRTL && styles.rtlContainer]}>
      <View style={[styles.header, isRTL && styles.rtlHeader]}>
        <Lightbulb size={16} color={colors.primary} />
        <Text style={[styles.headerText, isRTL && styles.rtlText]}>
          AI Analysis
        </Text>
      </View>
      
      <Text style={[styles.summaryText, isRTL && styles.rtlText]}>
        {summary}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.blue[50],
    borderLeftWidth: 3,
    borderLeftColor: colors.primary,
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  rtlContainer: {
    borderLeftWidth: 0,
    borderRightWidth: 3,
    borderRightColor: colors.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  rtlHeader: {
    flexDirection: 'row-reverse',
  },
  headerText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.primary,
    marginLeft: 6,
  },
  rtlText: {
    textAlign: 'right',
    marginLeft: 0,
    marginRight: 6,
  },
  summaryText: {
    fontSize: 15,
    lineHeight: 22,
    color: colors.gray[800],
  },
});
