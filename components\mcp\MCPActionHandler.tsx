import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import { MCPSuggestedAction } from '@/services/ai-assistant';
import { mcpServerService } from '@/services/mcp-server';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/i18n/useTranslation';
import { 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Server,
  Database,
  Zap
} from 'lucide-react-native';

interface MCPActionHandlerProps {
  actions: MCPSuggestedAction[];
  onActionComplete: (action: MCPSuggestedAction, result: any) => void;
  onAllActionsComplete: () => void;
}

export default function MCPActionHandler({
  actions,
  onActionComplete,
  onAllActionsComplete,
}: MCPActionHandlerProps) {
  const { t, isRTL } = useTranslation();
  const { currentFarm } = useFarmStore();
  const { user } = useAuthStore();
  
  const [executingActions, setExecutingActions] = useState<Set<string>>(new Set());
  const [completedActions, setCompletedActions] = useState<Set<string>>(new Set());
  const [actionResults, setActionResults] = useState<Map<string, any>>(new Map());

  const executeAction = async (action: MCPSuggestedAction) => {
    if (!currentFarm || !user) {
      Alert.alert('Error', 'No farm or user selected');
      return;
    }

    setExecutingActions(prev => new Set(prev).add(action.id));

    try {
      const context = {
        farmId: currentFarm.id,
        userId: user.id || user.uid || '123456',
      };

      // Execute action through MCP server (server-side processing)
      const result = await mcpServerService.executeAction(action, context);
      
      // Mark action as completed
      setCompletedActions(prev => new Set(prev).add(action.id));
      setActionResults(prev => new Map(prev).set(action.id, result));
      
      // Notify parent component
      onActionComplete(action, result);
      
      Alert.alert(
        'Action Completed', 
        `${action.title} executed successfully via MCP server!`,
        [{ text: 'OK' }]
      );

      // Check if all actions are completed
      const newCompletedCount = completedActions.size + 1;
      if (newCompletedCount === actions.length) {
        setTimeout(() => {
          onAllActionsComplete();
        }, 1000);
      }

    } catch (error) {
      console.error('MCP Action execution error:', error);
      Alert.alert(
        'Action Failed', 
        `Failed to execute "${action.title}": ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    } finally {
      setExecutingActions(prev => {
        const newSet = new Set(prev);
        newSet.delete(action.id);
        return newSet;
      });
    }
  };

  const getActionIcon = (action: MCPSuggestedAction) => {
    const iconProps = { size: 18, color: colors.white };
    
    if (completedActions.has(action.id)) {
      return <CheckCircle {...iconProps} color={colors.success} />;
    }
    
    if (executingActions.has(action.id)) {
      return <Clock {...iconProps} color={colors.warning} />;
    }

    switch (action.type) {
      case 'save':
        return <Database {...iconProps} />;
      case 'get':
        return <Server {...iconProps} />;
      case 'update':
        return <Zap {...iconProps} />;
      case 'delete':
        return <AlertCircle {...iconProps} color={colors.danger} />;
      default:
        return <Server {...iconProps} />;
    }
  };

  const getActionButtonStyle = (action: MCPSuggestedAction) => {
    if (completedActions.has(action.id)) {
      return [styles.actionButton, styles.completedButton];
    }
    
    if (executingActions.has(action.id)) {
      return [styles.actionButton, styles.executingButton];
    }

    switch (action.type) {
      case 'save':
        return [styles.actionButton, styles.saveButton];
      case 'get':
        return [styles.actionButton, styles.getButton];
      case 'update':
        return [styles.actionButton, styles.updateButton];
      case 'delete':
        return [styles.actionButton, styles.deleteButton];
      default:
        return [styles.actionButton];
    }
  };

  return (
    <View style={[styles.container, isRTL && styles.rtlContainer]}>
      <View style={[styles.header, isRTL && styles.rtlHeader]}>
        <Server size={20} color={colors.primary} />
        <Text style={[styles.headerText, isRTL && styles.rtlText]}>
          MCP Server Actions
        </Text>
      </View>

      <Text style={[styles.description, isRTL && styles.rtlText]}>
        These actions will be processed by the MCP server:
      </Text>

      {actions.map((action) => {
        const isExecuting = executingActions.has(action.id);
        const isCompleted = completedActions.has(action.id);
        const result = actionResults.get(action.id);

        return (
          <View key={action.id} style={[styles.actionContainer, isRTL && styles.rtlActionContainer]}>
            <TouchableOpacity
              style={getActionButtonStyle(action)}
              onPress={() => !isCompleted && !isExecuting && executeAction(action)}
              disabled={isCompleted || isExecuting}
            >
              <View style={[styles.actionContent, isRTL && styles.rtlActionContent]}>
                {isExecuting ? (
                  <ActivityIndicator size="small" color={colors.white} />
                ) : (
                  getActionIcon(action)
                )}
                
                <View style={styles.actionTextContainer}>
                  <Text style={[styles.actionTitle, isRTL && styles.rtlText]}>
                    {action.title}
                  </Text>
                  <Text style={[styles.actionDescription, isRTL && styles.rtlText]}>
                    {action.description}
                  </Text>
                  <Text style={[styles.actionType, isRTL && styles.rtlText]}>
                    {action.type.toUpperCase()} • {action.entity.toUpperCase()}
                  </Text>
                </View>

                {isCompleted && (
                  <CheckCircle size={20} color={colors.success} />
                )}
              </View>
            </TouchableOpacity>

            {/* Show result if completed */}
            {isCompleted && result && (
              <View style={[styles.resultContainer, isRTL && styles.rtlResultContainer]}>
                <Text style={[styles.resultText, isRTL && styles.rtlText]}>
                  ✅ {result.message || 'Action completed successfully'}
                </Text>
              </View>
            )}
          </View>
        );
      })}

      <View style={[styles.footer, isRTL && styles.rtlFooter]}>
        <Text style={[styles.footerText, isRTL && styles.rtlText]}>
          Completed: {completedActions.size}/{actions.length}
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginTop: 12,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  rtlContainer: {
    // RTL specific styles
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  rtlHeader: {
    flexDirection: 'row-reverse',
  },
  headerText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginLeft: 8,
  },
  rtlText: {
    textAlign: 'right',
    marginLeft: 0,
    marginRight: 8,
  },
  description: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 16,
    lineHeight: 20,
  },
  actionContainer: {
    marginBottom: 12,
  },
  rtlActionContainer: {
    // RTL specific styles
  },
  actionButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    padding: 12,
  },
  saveButton: {
    backgroundColor: colors.success,
  },
  getButton: {
    backgroundColor: colors.info,
  },
  updateButton: {
    backgroundColor: colors.warning,
  },
  deleteButton: {
    backgroundColor: colors.danger,
  },
  completedButton: {
    backgroundColor: colors.gray[400],
  },
  executingButton: {
    backgroundColor: colors.warning,
  },
  actionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rtlActionContent: {
    flexDirection: 'row-reverse',
  },
  actionTextContainer: {
    flex: 1,
    marginLeft: 12,
  },
  actionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.white,
    marginBottom: 2,
  },
  actionDescription: {
    fontSize: 12,
    color: colors.white,
    opacity: 0.9,
    marginBottom: 2,
  },
  actionType: {
    fontSize: 10,
    color: colors.white,
    opacity: 0.8,
    fontWeight: '600',
  },
  resultContainer: {
    backgroundColor: colors.green[50],
    padding: 8,
    borderRadius: 6,
    marginTop: 8,
  },
  rtlResultContainer: {
    // RTL specific styles
  },
  resultText: {
    fontSize: 12,
    color: colors.success,
    fontWeight: '500',
  },
  footer: {
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    paddingTop: 12,
    marginTop: 8,
  },
  rtlFooter: {
    // RTL specific styles
  },
  footerText: {
    fontSize: 12,
    color: colors.gray[600],
    textAlign: 'center',
    fontWeight: '600',
  },
});
