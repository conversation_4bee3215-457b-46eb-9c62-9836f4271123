import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
} from 'react-native';
import { ChatMessage } from '@/services/ai-assistant';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/i18n/useTranslation';
import MCPEntityDataDisplay from './MCPEntityDataDisplay';
import MCPSummaryDisplay from './MCPSummaryDisplay';

interface MCPMessageComponentProps {
  message: ChatMessage;
  onActionPress: (actions: any[]) => void;
  onImagePress: (imageUrl: string) => void;
}

export default function MCPMessageComponent({
  message,
  onActionPress,
  onImagePress,
}: MCPMessageComponentProps) {
  const { t, isRTL } = useTranslation();

  return (
    <View style={[
      styles.messageContainer,
      message.role === 'user' ? styles.userMessage : styles.assistantMessage,
      isRTL && styles.rtlMessage
    ]}>
      {/* User Message Content */}
      {message.role === 'user' && (
        <>
          <Text style={[
            styles.messageText,
            styles.userMessageText,
            isRTL && styles.rtlText
          ]}>
            {message.content}
          </Text>

          {/* User Images */}
          {message.images && message.images.length > 0 && (
            <View style={styles.imagesContainer}>
              {message.images.map((uri, index) => (
                <TouchableOpacity
                  key={index}
                  onPress={() => onImagePress(uri)}
                >
                  <Image 
                    source={{ uri }} 
                    style={styles.messageImage} 
                    resizeMode="cover"
                  />
                </TouchableOpacity>
              ))}
            </View>
          )}
        </>
      )}

      {/* MCP Assistant Response */}
      {message.role === 'assistant' && (
        <>
          {/* MCP Summary Display */}
          {message.metadata?.summary && (
            <MCPSummaryDisplay 
              summary={message.metadata.summary}
              isRTL={isRTL}
            />
          )}

          {/* MCP Entity Data Display */}
          {message.metadata?.entityData && (
            <MCPEntityDataDisplay 
              entityData={message.metadata.entityData}
              isRTL={isRTL}
            />
          )}

          {/* MCP Actions Button */}
          {message.metadata?.suggestedActionArray && 
           message.metadata.suggestedActionArray.length > 0 && (
            <TouchableOpacity
              style={[styles.actionsButton, isRTL && styles.rtlActionsButton]}
              onPress={() => onActionPress(message.metadata!.suggestedActionArray!)}
            >
              <Text style={[styles.actionsButtonText, isRTL && styles.rtlText]}>
                View Actions ({message.metadata.suggestedActionArray.length})
              </Text>
            </TouchableOpacity>
          )}
        </>
      )}

      {/* Timestamp */}
      <Text style={[styles.timestamp, isRTL && styles.rtlTimestamp]}>
        {message.timestamp.toLocaleTimeString()}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  messageContainer: {
    marginBottom: 16,
    padding: 12,
    borderRadius: 12,
    maxWidth: '80%',
  },
  userMessage: {
    alignSelf: 'flex-end',
    backgroundColor: colors.primary,
  },
  assistantMessage: {
    alignSelf: 'flex-start',
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  rtlMessage: {
    alignSelf: 'flex-start',
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  userMessageText: {
    color: colors.white,
  },
  rtlText: {
    textAlign: 'right',
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
    gap: 8,
  },
  messageImage: {
    width: 100,
    height: 100,
    borderRadius: 8,
  },
  actionsButton: {
    marginTop: 12,
    padding: 12,
    backgroundColor: colors.primary,
    borderRadius: 8,
    alignItems: 'center',
  },
  rtlActionsButton: {
    alignItems: 'center',
  },
  actionsButtonText: {
    color: colors.white,
    fontSize: 14,
    fontWeight: '600',
  },
  timestamp: {
    fontSize: 12,
    color: colors.gray[500],
    marginTop: 8,
    textAlign: 'right',
  },
  rtlTimestamp: {
    textAlign: 'left',
  },
});
