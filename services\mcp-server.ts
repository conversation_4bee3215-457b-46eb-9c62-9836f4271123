import * as FileSystem from 'expo-file-system';
import { MCPResponse, MCPSuggestedAction } from './ai-assistant';
import { MCPFirestoreOperations, MCPFirestoreContext } from './mcp-firestore-operations';

export interface MCPServerConfig {
  baseUrl: string;
  apiKey?: string;
  timeout?: number;
}

export interface MCPMessageRequest {
  message: string;
  images?: string[];
  context?: {
    farmId: string;
    userId: string;
    language: string;
    currentEntities?: any[];
  };
}

export class MCPServerService {
  private config: MCPServerConfig;

  constructor(config: MCPServerConfig) {
    this.config = {
      timeout: 30000, // 30 seconds default
      ...config,
    };
  }

  /**
   * Send a message to the MCP server and get AI analysis
   */
  async analyzeMessage(request: MCPMessageRequest): Promise<MCPResponse> {
    try {
      const payload = await this.buildRequestPayload(request);

      // Create a timeout promise for React Native compatibility
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Request timeout')), this.config.timeout!);
      });

      const fetchPromise = fetch(`${this.config.baseUrl}/chat/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` }),
        },
        body: JSON.stringify(payload),
      });

      const response = await Promise.race([fetchPromise, timeoutPromise]) as Response;

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `MCP Server error: ${response.status}`);
      }

      const data = await response.json();
      return this.validateAndTransformResponse(data);
    } catch (error) {
      console.error('MCP Server analysis error:', error);
      throw error;
    }
  }

  /**
   * Execute a specific action through the MCP server or locally via Firestore
   */
  async executeAction(action: MCPSuggestedAction, context: MCPFirestoreContext): Promise<any> {
    try {
      // Validate action before execution
      const validation = MCPFirestoreOperations.validateAction(action);
      if (!validation.isValid) {
        throw new Error(`Action validation failed: ${validation.errors.join(', ')}`);
      }

      // Try to execute locally via Firestore first
      try {
        const result = await MCPFirestoreOperations.executeAction(action, context);
        console.log('Action executed locally via Firestore:', result);
        return result;
      } catch (firestoreError) {
        console.warn('Local Firestore execution failed, trying MCP server:', firestoreError);

        // Fallback to MCP server execution
        const payload = {
          action,
          context,
        };

        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Request timeout')), this.config.timeout!);
        });

        const fetchPromise = fetch(`${this.config.baseUrl}/execute-action`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` }),
          },
          body: JSON.stringify(payload),
        });

        const response = await Promise.race([fetchPromise, timeoutPromise]) as Response;

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.message || `Action execution failed: ${response.status}`);
        }

        return await response.json();
      }
    } catch (error) {
      console.error('Action execution error:', error);
      throw error;
    }
  }

  /**
   * Build the request payload for the MCP server
   */
  private async buildRequestPayload(request: MCPMessageRequest): Promise<any> {
    const payload: any = {
      text: request.message,
      userId: request.context?.userId || '123456',
    };

    // Handle single image if provided (your API expects single image)
    if (request.images && request.images.length > 0) {
      try {
        const imageUri = request.images[0]; // Take first image
        const base64 = await FileSystem.readAsStringAsync(imageUri, {
          encoding: FileSystem.EncodingType.Base64,
        });

        payload.imageBase64 = base64;
        payload.imageName = 'photo.jpg';
        payload.imageMimeType = 'image/jpeg';
      } catch (error) {
        console.error('Error processing image:', error);
        // Continue without image if processing fails
      }
    }

    return payload;
  }

  /**
   * Validate and transform the MCP server response
   */
  private validateAndTransformResponse(data: any): MCPResponse {
    // Handle your server's response format
    const suggestions = data.suggestions;

    if (!suggestions || !suggestions.summary) {
      throw new Error('Invalid MCP response: missing suggestions or summary');
    }

    // Transform suggested actions from your format to our format
    const suggestedActionArray: MCPSuggestedAction[] = (suggestions.suggestedActions || []).map((action: any, index: number) => {
      return {
        id: `action_${Date.now()}_${index}`,
        type: this.mapActionType(action.type),
        entity: this.mapEntityType(action.entityType),
        title: action.label || `${action.type} ${action.entityType}`,
        description: `Execute ${action.type} for ${action.entityType}`,
        data: suggestions.entityData, // Use entityData as the data for the action
        confidence: 0.9,
      };
    });

    return {
      summary: suggestions.summary,
      entityData: suggestions.entityData,
      suggestedActionArray,
    };
  }

  /**
   * Map your server's action types to our action types
   */
  private mapActionType(serverActionType: string): 'save' | 'get' | 'update' | 'delete' {
    const actionMap: { [key: string]: 'save' | 'get' | 'update' | 'delete' } = {
      'add_plant': 'save',
      'add_animal': 'save',
      'add_equipment': 'save',
      'add_garden': 'save',
      'add_field': 'save',
      'update_plant': 'update',
      'update_animal': 'update',
      'get_plants': 'get',
      'get_animals': 'get',
      'delete_plant': 'delete',
      'delete_animal': 'delete',
    };

    return actionMap[serverActionType] || 'save';
  }

  /**
   * Map your server's entity types to our entity types
   */
  private mapEntityType(serverEntityType: string): 'animal' | 'plant' | 'garden' | 'field' | 'equipment' | 'crop' | 'task' {
    const entityMap: { [key: string]: 'animal' | 'plant' | 'garden' | 'field' | 'equipment' | 'crop' | 'task' } = {
      'plant': 'plant',
      'animal': 'animal',
      'equipment': 'equipment',
      'garden': 'garden',
      'field': 'field',
      'crop': 'crop',
      'task': 'task',
    };

    return entityMap[serverEntityType] || 'plant';
  }

  /**
   * Test connection to the MCP server
   */
  async testConnection(): Promise<boolean> {
    try {
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Connection timeout')), 5000);
      });

      const fetchPromise = fetch(`${this.config.baseUrl}/health`, {
        method: 'GET',
        headers: {
          ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` }),
        },
      });

      const response = await Promise.race([fetchPromise, timeoutPromise]) as Response;
      return response.ok;
    } catch (error) {
      console.error('MCP Server connection test failed:', error);
      return false;
    }
  }
}

// Default MCP server configuration
export const defaultMCPConfig: MCPServerConfig = {
  baseUrl: 'https://api-3tfznjgouq-uc.a.run.app',
  apiKey: process.env.EXPO_PUBLIC_MCP_API_KEY,
  timeout: 30000,
};

// Singleton instance
export const mcpServerService = new MCPServerService(defaultMCPConfig);
