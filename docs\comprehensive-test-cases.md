# Comprehensive Test Cases for Farm Management App

## 1. Authentication Module

### Test Case 1.1: User Registration
**Objective**: Verify user can register successfully
**Steps**:
1. Open app and navigate to registration screen
2. Enter valid email, password, and confirm password
3. Enter full name and select user role
4. Tap "Register" button
**Expected Result**: User account created, verification email sent, redirected to login

### Test Case 1.2: User Login
**Objective**: Verify user can login with valid credentials
**Steps**:
1. Enter registered email and password
2. Tap "Login" button
**Expected Result**: User authenticated and redirected to dashboard

### Test Case 1.3: Password Reset
**Objective**: Verify password reset functionality
**Steps**:
1. Tap "Forgot Password" on login screen
2. Enter registered email
3. Tap "Send Reset Email"
**Expected Result**: Password reset email sent, user can reset password

## 2. Farm Management Module

### Test Case 2.1: Create Farm
**Objective**: Verify farm creation functionality
**Steps**:
1. Navigate to farm creation screen
2. Enter farm name, description, location
3. Select farm type and size
4. Add farm image (optional)
5. Tap "Create Farm"
**Expected Result**: Farm created successfully, appears in farm list

### Test Case 2.2: Farm Selection and Persistence
**Objective**: Verify farm selection persists across app sessions
**Steps**:
1. Select a farm from farm selector
2. Close and reopen app
3. Check if same farm is still selected
**Expected Result**: Previously selected farm remains active

### Test Case 2.3: Farm Data Refresh
**Objective**: Verify pull-to-refresh doesn't change selected farm
**Steps**:
1. Select a specific farm (not the first one)
2. Pull down to refresh on dashboard
3. Check if selected farm remains the same
**Expected Result**: Selected farm should not change to first farm

## 3. Garden Management Module

### Test Case 3.1: Create Garden
**Objective**: Verify garden creation with location
**Steps**:
1. Navigate to garden creation screen
2. Enter garden name, type, size
3. Select location on map
4. Add soil type and irrigation system
5. Tap "Create Garden"
**Expected Result**: Garden created with location data

### Test Case 3.2: Garden Details Screen
**Objective**: Verify garden details display without infinite loops
**Steps**:
1. Navigate to garden details screen
2. Switch between different tabs (Details, Plants, Gallery)
3. Check for any performance issues or crashes
**Expected Result**: Screen loads smoothly without infinite re-renders

### Test Case 3.3: Garden Location on Maps
**Objective**: Verify gardens appear on maps screen
**Steps**:
1. Create garden with valid coordinates
2. Navigate to maps screen
3. Select "Gardens" tab
4. Check if garden appears on map
**Expected Result**: Garden marker visible on map with correct location

## 4. Plant Management Module

### Test Case 4.1: Add Plant to Garden
**Objective**: Verify plant can be added to garden
**Steps**:
1. Navigate to plant creation screen
2. Enter plant name, species, variety
3. Select garden from dropdown
4. Set planting date and expected harvest
5. Tap "Add Plant"
**Expected Result**: Plant added to selected garden

### Test Case 4.2: Plant Health Tracking
**Objective**: Verify plant health check functionality
**Steps**:
1. Navigate to plant details screen
2. Add health check entry
3. Update plant status and health
4. Save changes
**Expected Result**: Health check recorded, plant status updated

## 5. Animal Management Module

### Test Case 5.1: Add Animal
**Objective**: Verify animal registration
**Steps**:
1. Navigate to animal creation screen
2. Enter animal details (species, breed, name)
3. Add identification number
4. Set field/location
5. Tap "Add Animal"
**Expected Result**: Animal registered successfully

### Test Case 5.2: Animal Health Monitoring
**Objective**: Verify animal health tracking
**Steps**:
1. Navigate to animal details screen
2. Add health check entry
3. Record vaccination or treatment
4. Update animal status
**Expected Result**: Health record saved, status updated

## 6. Task Management Module

### Test Case 6.1: Create Task
**Objective**: Verify task creation and assignment
**Steps**:
1. Navigate to task creation screen
2. Enter task title and description
3. Set due date and priority
4. Assign to team member
5. Add checklist items
6. Tap "Create Task"
**Expected Result**: Task created and assigned

### Test Case 6.2: Complete Task
**Objective**: Verify task completion workflow
**Steps**:
1. Navigate to assigned task
2. Complete checklist items
3. Add evidence photos
4. Add completion notes
5. Mark task as complete
**Expected Result**: Task marked complete with evidence

## 7. Maps and Location Module

### Test Case 7.1: Entity Location Display
**Objective**: Verify all entities with location appear on maps
**Steps**:
1. Create entities (gardens, fields, animals) with locations
2. Navigate to maps screen
3. Switch between different entity tabs
4. Verify markers appear correctly
**Expected Result**: All entities with valid coordinates visible on map

### Test Case 7.2: Map Navigation
**Objective**: Verify map navigation to entity details
**Steps**:
1. Tap on entity marker on map
2. Tap on callout/info window
3. Verify navigation to entity details
**Expected Result**: Correct entity details screen opens

## 8. AI Assistant Module

### Test Case 8.1: Text Message Analysis
**Objective**: Verify AI can analyze text messages
**Steps**:
1. Open AI assistant
2. Send text message: "I want to add a tomato plant"
3. Review AI response
4. Check for suggested actions
**Expected Result**: AI provides relevant response with plant creation suggestion

### Test Case 8.2: Image Analysis
**Objective**: Verify AI can analyze images
**Steps**:
1. Open AI assistant
2. Take photo of plant/animal
3. Send image with text description
4. Review AI analysis
**Expected Result**: AI identifies entity in image and provides insights

### Test Case 8.3: Entity Navigation from AI
**Objective**: Verify navigation to entity details from AI responses
**Steps**:
1. Ask AI about specific entities
2. Tap on entity reference cards in response
3. Verify navigation to entity details
**Expected Result**: Correct entity details screen opens

### Test Case 8.4: Action Execution
**Objective**: Verify AI suggested actions can be executed
**Steps**:
1. Get AI suggestion to create/update entity
2. Tap on suggested action
3. Review and confirm action
4. Verify entity is created/updated
**Expected Result**: Entity successfully created/updated in database

## 9. MCP AI Assistant Module

### Test Case 9.1: MCP Server Connection
**Objective**: Verify connection to MCP server
**Steps**:
1. Navigate to Profile > MCP AI Assistant
2. Send test message
3. Verify server response
**Expected Result**: Successful connection and response from MCP server

### Test Case 9.2: MCP Image Upload
**Objective**: Verify single image upload to MCP
**Steps**:
1. Open MCP chat
2. Take/select single image
3. Add text message
4. Send combined message
**Expected Result**: Image and text sent to MCP server, analysis received

### Test Case 9.3: MCP Action Execution
**Objective**: Verify MCP suggested actions work
**Steps**:
1. Get MCP response with suggested actions
2. Tap action button
3. Execute suggested action
4. Verify data saved to Firestore
**Expected Result**: Action executed successfully, data persisted

## 10. Data Persistence and Sync Module

### Test Case 10.1: Offline Data Persistence
**Objective**: Verify data persists when offline
**Steps**:
1. Create entities while online
2. Turn off internet connection
3. View created entities
4. Turn internet back on
**Expected Result**: Data remains accessible offline, syncs when online

### Test Case 10.2: Real-time Sync
**Objective**: Verify real-time data synchronization
**Steps**:
1. Have two devices logged into same farm
2. Create entity on device 1
3. Check if entity appears on device 2
**Expected Result**: Entity appears on device 2 in real-time

## 11. Internationalization Module

### Test Case 11.1: Language Switching
**Objective**: Verify app language can be changed
**Steps**:
1. Navigate to Profile > Language
2. Switch from English to Urdu
3. Navigate through app screens
**Expected Result**: All text displays in selected language

### Test Case 11.2: RTL Layout
**Objective**: Verify right-to-left layout for Urdu
**Steps**:
1. Switch language to Urdu
2. Navigate through different screens
3. Check layout orientation
**Expected Result**: Layout properly adjusted for RTL reading

## 12. Performance and Error Handling

### Test Case 12.1: Large Dataset Performance
**Objective**: Verify app performance with large datasets
**Steps**:
1. Create 100+ entities of each type
2. Navigate through different screens
3. Monitor app performance and memory usage
**Expected Result**: App remains responsive with large datasets

### Test Case 12.2: Network Error Handling
**Objective**: Verify graceful handling of network errors
**Steps**:
1. Disconnect internet during data operations
2. Attempt to create/update entities
3. Check error messages and recovery
**Expected Result**: Appropriate error messages, data queued for sync

## Test Execution Guidelines

1. **Test Environment**: Use both iOS and Android devices
2. **Test Data**: Use realistic farm data for testing
3. **Performance Monitoring**: Monitor memory usage and response times
4. **Error Logging**: Capture and analyze error logs
5. **User Experience**: Focus on smooth user interactions
6. **Edge Cases**: Test with empty states, invalid data, network issues

## 13. Equipment Management Module

### Test Case 13.1: Add Equipment
**Objective**: Verify equipment registration
**Steps**:
1. Navigate to equipment creation screen
2. Enter equipment details (name, type, manufacturer)
3. Add serial number and purchase date
4. Set maintenance schedule
5. Tap "Add Equipment"
**Expected Result**: Equipment registered with maintenance tracking

### Test Case 13.2: Equipment Maintenance Tracking
**Objective**: Verify maintenance scheduling and tracking
**Steps**:
1. Navigate to equipment details
2. Add maintenance record
3. Schedule next maintenance
4. Check maintenance alerts
**Expected Result**: Maintenance tracked, alerts generated

## 14. Financial Management Module

### Test Case 14.1: Record Income
**Objective**: Verify income recording functionality
**Steps**:
1. Navigate to financial records
2. Add income entry (crop sale, etc.)
3. Enter amount, date, and category
4. Attach receipt image
5. Save record
**Expected Result**: Income recorded and categorized

### Test Case 14.2: Expense Tracking
**Objective**: Verify expense recording
**Steps**:
1. Add expense entry
2. Select category and payment method
3. Enter amount and description
4. Attach receipt
5. Save expense
**Expected Result**: Expense recorded and categorized

## 15. Crop Management Module

### Test Case 15.1: Crop Planting
**Objective**: Verify crop planting workflow
**Steps**:
1. Navigate to field details
2. Add new crop planting
3. Select crop type and variety
4. Set planting date and expected harvest
5. Record planting details
**Expected Result**: Crop planting recorded with timeline

### Test Case 15.2: Harvest Recording
**Objective**: Verify harvest recording
**Steps**:
1. Navigate to mature crop
2. Record harvest details
3. Enter quantity and quality
4. Add harvest photos
5. Update crop status
**Expected Result**: Harvest recorded, crop status updated

## 16. Weather Integration Module

### Test Case 16.1: Weather Data Display
**Objective**: Verify weather information display
**Steps**:
1. Ensure farm has location set
2. Navigate to dashboard
3. Check weather widget
4. Verify current and forecast data
**Expected Result**: Accurate weather data displayed

### Test Case 16.2: Weather Alerts
**Objective**: Verify weather-based alerts
**Steps**:
1. Monitor for severe weather conditions
2. Check if alerts are generated
3. Verify alert notifications
**Expected Result**: Timely weather alerts received

## 17. Inventory Management Module

### Test Case 17.1: Seed Inventory
**Objective**: Verify seed inventory tracking
**Steps**:
1. Add seed inventory items
2. Record usage during planting
3. Update inventory levels
4. Check low stock alerts
**Expected Result**: Inventory accurately tracked with alerts

### Test Case 17.2: Feed Inventory
**Objective**: Verify animal feed inventory
**Steps**:
1. Add feed inventory
2. Record daily feed consumption
3. Update stock levels
4. Generate reorder alerts
**Expected Result**: Feed inventory managed with consumption tracking

## Automated Testing Recommendations

1. **Unit Tests**: Test individual functions and components
2. **Integration Tests**: Test module interactions
3. **E2E Tests**: Test complete user workflows
4. **Performance Tests**: Test with large datasets
5. **Accessibility Tests**: Ensure app is accessible to all users

## Critical Bug Fixes Verification

### Fix 1: Garden Details Infinite Loop
**Test**: Navigate to garden details multiple times, check for performance issues
**Expected**: No infinite re-renders, smooth navigation

### Fix 2: Dashboard Refresh Farm Selection
**Test**: Select non-first farm, pull to refresh, verify farm selection persists
**Expected**: Selected farm remains unchanged after refresh

### Fix 3: Maps Garden Display
**Test**: Create gardens with location, check maps screen
**Expected**: Gardens appear on maps with correct markers

### Fix 4: MCP Server Integration
**Test**: Send messages to MCP server, verify responses and actions
**Expected**: Successful communication with MCP server
