import React, { useState, useMemo, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  Modal,
  FlatList,
  Alert,
  TextInput,
  ActivityIndicator,
  I18nManager,
} from 'react-native';
import { router } from 'expo-router';
import { colors } from '@/constants/colors';
import { useAuthStore } from '@/store/auth-store';
import { useFarmStore } from '@/store/farm-store';
import {
  User,
  Settings,
  ChevronRight,
  Globe,
  Bell,
  Shield,
  HelpCircle,
  LogOut,
  CheckCircle,
  Clock,
  Check,
  Lock,
  Eye,
  EyeOff,
  ChevronLeft,
  MessageCircle,
} from 'lucide-react-native';
import { useTranslation } from '@/i18n/useTranslation';
import { useLanguage } from '@/i18n/translations/LanguageProvider';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function ProfileScreen() {
  // Use specific selectors to avoid unnecessary re-renders and infinite loops
  const user = useAuthStore(state => state.user);
  const signOut = useAuthStore(state => state.signOut);
  const clearError = useAuthStore(state => state.clearError);
  const updateUserProfile = useAuthStore(state => state.updateUserProfile);
  const setAppLanguage = useAuthStore(state => state.setLanguage);

  // Use a separate selector for tasks to prevent infinite loops
  const tasks = useFarmStore(state => state.tasks);
  // n18 comment
  const { t, locale, setLocale, isRTL } = useTranslation();


  // const { t, setLocale, locale, isRTL } = useLanguage();
  const [showLanguageModal, setShowLanguageModal] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);

  // Password change state
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [passwordError, setPasswordError] = useState('');

  // Sync locale with auth store language
  // useEffect(() => {
  //   if (user?.preferredLanguage && user.preferredLanguage !== locale) {
  //     setLocale(user.preferredLanguage);
  //   }
  // }, [user, locale, setLocale]);

  const handleLogout = async () => {
    try {
      clearError();
      await signOut();
      router.replace('/(auth)');
    } catch (error) {
      console.error('Logout error:', error);
      Alert.alert('Error', 'Failed to log out. Please try again.');
    }
  };

  const handleChangePassword = async () => {
    setPasswordError('');

    if (!currentPassword) {
      setPasswordError(t('profile.currentPasswordRequired'));
      return;
    }

    if (!newPassword) {
      setPasswordError(t('profile.newPasswordRequired'));
      return;
    }

    if (newPassword.length < 8) {
      setPasswordError(t('login.passwordTooShort'));
      return;
    }

    if (newPassword !== confirmPassword) {
      setPasswordError(t('profile.passwordsDoNotMatch'));
      return;
    }

    setIsChangingPassword(true);

    try {
      await updateUserProfile({ password: newPassword });
      setShowPasswordModal(false);
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
      Alert.alert(t('common.success'), t('profile.passwordChanged'));
    } catch (error: any) {
      setPasswordError(error.message || t('profile.passwordError'));
    } finally {
      setIsChangingPassword(false);
    }
  };

  const handleChangeLanguage = async (newLocale: string) => {
    try {

      Alert.alert('Restarting app...' + newLocale);
      // Update both i18n system and auth store
      await setLocale(newLocale);
      await setAppLanguage(newLocale);
      // // Update language in i18n system
      // await setLocale(newLocale);

      // // Update language in auth store and user profile
      // await setAppLanguage(newLocale);

      // // Update user profile in Firestore
      // if (user) {
      //   await updateUserProfile({ preferredLanguage: newLocale });
      // }

      setShowLanguageModal(false);
    } catch (error) {
      console.error('Language change error:', error);
      Alert.alert('Error', 'Failed to change language. Please try again.');
    }
  };

  // Memoize calculations to prevent infinite loops
  const stats = useMemo(() => {
    if (!user || !tasks.length) {
      return { totalTasks: 0, completedTasks: 0 };
    }

    const totalTasks = tasks.filter(task => task.assignedTo === user.id).length;
    const completedTasks = tasks.filter(task =>
      task.assignedTo === user.id && task.status === 'completed'
    ).length;
    return { totalTasks, completedTasks };
  }, [user, tasks]);

  // Memoize recent activity to prevent infinite loops
  const recentActivity = useMemo(() => {
    if (!user || !tasks.length) {
      return [];
    }

    return tasks
      .filter(task =>
        task.assignedTo === user?.id ||
        task.assignedBy === user?.id
      )
      .sort((a, b) => {
        const dateA = new Date(a.updatedAt || a.createdAt);
        const dateB = new Date(b.updatedAt || b.createdAt);
        return dateB.getTime() - dateA.getTime();
      })
      .slice(0, 3);
  }, [user, tasks]);

  const languages = useMemo(() => [
    { id: 'en', name: 'English', nativeName: 'English' },
    { id: 'ur', name: 'Urdu', nativeName: 'اردو' },
  ], []);

  const renderLanguageItem = ({ item }) => (
    <TouchableOpacity
      style={styles.modalItem}
      onPress={() => handleChangeLanguage(item.id)}
    >
      <View style={styles.modalItemContent}>
        <Globe size={20} color={colors.gray[600]} style={styles.modalItemIcon} />
        <View>
          <Text style={styles.modalItemText}>{item.name}</Text>
          <Text style={styles.modalItemSubtext}>{item.nativeName}</Text>
        </View>
      </View>
      {locale === item.id && (
        <Check size={20} color={colors.primary} />
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <View style={styles.profileImageContainer}>
            {user?.avatar ? (
              <Image
                source={{ uri: user.avatar }}
                style={styles.profileImage}
              />
            ) : (
              <View style={styles.profileImagePlaceholder}>
                <User size={40} color={colors.white} />
              </View>
            )}
            <View style={styles.editButton}>
              <Text style={styles.editButtonText}>{t('common.edit')}</Text>
            </View>
          </View>

          <Text style={styles.userName}>{user?.name}</Text>
          <Text style={styles.userEmail}>{user?.email}</Text>

          <View style={styles.roleBadge}>
            <Text style={styles.roleText}>
              {user?.role === 'owner'
                ? t('profile.farmAdministrator')
                : user?.role === 'admin'
                  ? t('profile.farmManager')
                  : t('profile.farmCaretaker')
              }
            </Text>
          </View>
        </View>

        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{stats.totalTasks}</Text>
            <Text style={styles.statLabel}>{t('profile.totalTasks')}</Text>
          </View>

          <View style={styles.statDivider} />

          <View style={styles.statItem}>
            <Text style={styles.statValue}>{stats.completedTasks}</Text>
            <Text style={styles.statLabel}>{t('profile.tasksDone')}</Text>
          </View>

          <View style={styles.statDivider} />

          <View style={styles.statItem}>
            <Text style={styles.statValue}>8</Text>
            <Text style={styles.statLabel}>{t('profile.fields')}</Text>
          </View>
        </View>

        <View style={[styles.sectionHeader, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
          <Text style={styles.sectionTitle}>{t('profile.recentActivity')}</Text>
        </View>

        {recentActivity.length > 0 ? (
          recentActivity.map((activity, index) => (
            <View key={index} style={styles.activityItem}>
              <View style={[
                styles.activityIcon,
                {
                  backgroundColor: activity.status === 'completed'
                    ? colors.success + '20'
                    : colors.primary + '20'
                }
              ]}>
                {activity.status === 'completed' ? (
                  <CheckCircle size={20} color={colors.success} />
                ) : (
                  <Clock size={20} color={colors.primary} />
                )}
              </View>

              <View style={styles.activityContent}>
                <Text style={styles.activityTitle}>
                  {activity.status === 'completed'
                    ? `${t('tasks.markAsCompleted')}: ${activity.title}`
                    : activity.title
                  }
                </Text>
                <Text style={styles.activityTime}>
                  {activity.status === 'completed' && activity.completedAt
                    ? new Date(activity.completedAt).toLocaleString([], {
                      hour: '2-digit',
                      minute: '2-digit',
                      weekday: 'short',
                    })
                    : new Date(activity.updatedAt || activity.createdAt).toLocaleString([], {
                      hour: '2-digit',
                      minute: '2-digit',
                      weekday: 'short',
                    })
                  }
                </Text>
              </View>
            </View>
          ))
        ) : (
          <View style={styles.emptyActivity}>
            <Text style={styles.emptyActivityText}>{t('profile.noRecentActivity')}</Text>
          </View>
        )}

        <View style={[styles.sectionHeader, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
          <Text style={styles.sectionTitle}>{t('profile.language')}</Text>
        </View>

        <TouchableOpacity
          style={[styles.settingItem, { flexDirection: isRTL ? "row-reverse" : "row" }]}
          onPress={() => setShowLanguageModal(true)}
        >
          <View style={styles.settingIcon}>
            <Globe size={20} color={colors.primary} />
          </View>
          <View style={[styles.settingContent]}>
            <Text style={[styles.settingText, { textAlign: isRTL ? 'right' : 'left' }]}>
              {locale === 'en' ? 'English' : 'اردو'}
            </Text>
          </View>

          {isRTL ? <ChevronLeft size={20} color={colors.gray[400]} /> : <ChevronRight size={20} color={colors.gray[400]} />}
          {/* <ChevronRight size={20} color={colors.gray[400]} /> */}
        </TouchableOpacity>

        <View style={[styles.sectionHeader, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
          <Text style={styles.sectionTitle}>{t('profile.security')}</Text>
        </View>

        <TouchableOpacity
          //  style={[styles.settingItem,{ flexDirection: isRTL? "row-reverse": "row"}]}
          style={[styles.settingItem, { flexDirection: isRTL ? "row-reverse" : "row" }]}
          onPress={() => setShowPasswordModal(true)}
        >
          <View style={styles.settingIcon}>
            <Lock size={20} color={colors.primary} />
          </View>
          <View style={styles.settingContent}>
            <Text style={[styles.settingText, { textAlign: isRTL ? 'right' : 'left' }]}>{t('profile.changePassword')}</Text>
          </View>
          {isRTL ? <ChevronLeft size={20} color={colors.gray[400]} /> : <ChevronRight size={20} color={colors.gray[400]} />}
          {/* <ChevronRight size={20} color={colors.gray[400]} /> */}
        </TouchableOpacity>

        <View style={[styles.sectionHeader, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
          <Text style={styles.sectionTitle}>{t('profile.notifications')}</Text>
        </View>

        <TouchableOpacity style={[styles.settingItem, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
          <View style={styles.settingIcon}>
            <Bell size={20} color={colors.primary} />
          </View>
          <View style={styles.settingContent}>
            <Text style={[styles.settingText, { textAlign: isRTL ? 'right' : 'left' }]}>{t('profile.notifications')}</Text>
          </View>

          {isRTL ? <ChevronLeft size={20} color={colors.gray[400]} /> : <ChevronRight size={20} color={colors.gray[400]} />}
          {/* <ChevronRight size={20} color={colors.gray[400]} /> */}
        </TouchableOpacity>

        <View style={[styles.sectionHeader, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
          <Text style={styles.sectionTitle}>{t('profile.accountSettings')}</Text>
        </View>

        <TouchableOpacity style={[styles.settingItem, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
          <View style={styles.settingIcon}>
            <Shield size={20} color={colors.primary} />
          </View>
          <View style={styles.settingContent}>
            <Text style={[styles.settingText, { textAlign: isRTL ? 'right' : 'left' }]}>{t('profile.accountSettings')}</Text>
          </View>
          {isRTL ? <ChevronLeft size={20} color={colors.gray[400]} /> : <ChevronRight size={20} color={colors.gray[400]} />}
          {/* <ChevronRight size={20} color={colors.gray[400]} /> */}
        </TouchableOpacity>

        <View style={[styles.sectionHeader, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
          <Text style={styles.sectionTitle}>{t('profile.helpSupport')}</Text>
        </View>

        <TouchableOpacity
          onPress={() => {
            console.log('Navigating to MCP chat...');
            try {
              router.push('/mcp-chat');
            } catch (error) {
              console.error('Navigation error:', error);
              Alert.alert('Navigation Error', 'Could not navigate to MCP chat screen');
            }
          }}
          style={[styles.settingItem, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
          <View style={styles.settingIcon}>
            <MessageCircle size={20} color={colors.primary} />
          </View>
          <View style={styles.settingContent}>
            <Text style={[styles.settingText, { textAlign: isRTL ? 'right' : 'left' }]}>MCP AI Assistant</Text>
            <Text style={[styles.settingSubtext, { textAlign: isRTL ? 'right' : 'left' }]}>Enhanced AI chat with smart actions</Text>
          </View>
          {isRTL ? <ChevronLeft size={20} color={colors.gray[400]} /> : <ChevronRight size={20} color={colors.gray[400]} />}
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            console.log('Navigating to test screen...');
            router.push('/test-screen')
          }}
          style={[styles.settingItem, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
          <View style={styles.settingIcon}>
            <MessageCircle size={20} color={colors.secondary} />
          </View>
          <View style={styles.settingContent}>
            <Text style={[styles.settingText, { textAlign: isRTL ? 'right' : 'left' }]}>Test Navigation</Text>
            <Text style={[styles.settingSubtext, { textAlign: isRTL ? 'right' : 'left' }]}>Test if navigation works</Text>
          </View>
          {isRTL ? <ChevronLeft size={20} color={colors.gray[400]} /> : <ChevronRight size={20} color={colors.gray[400]} />}
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            console.log('Navigating to MCP chat (no hyphen)...');
            router.push('/mcpchat');
          }}
          style={[styles.settingItem, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
          <View style={styles.settingIcon}>
            <MessageCircle size={20} color={colors.success} />
          </View>
          <View style={styles.settingContent}>
            <Text style={[styles.settingText, { textAlign: isRTL ? 'right' : 'left' }]}>MCP Chat (No Hyphen)</Text>
            <Text style={[styles.settingSubtext, { textAlign: isRTL ? 'right' : 'left' }]}>Test MCP chat without hyphen</Text>
          </View>
          {isRTL ? <ChevronLeft size={20} color={colors.gray[400]} /> : <ChevronRight size={20} color={colors.gray[400]} />}
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => router.push('/help/HelpVideosScreen')}
          style={[styles.settingItem, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
          <View style={styles.settingIcon}>
            <HelpCircle size={20} color={colors.primary} />
          </View>
          <View style={styles.settingContent}>
            <Text style={[styles.settingText, { textAlign: isRTL ? 'right' : 'left' }]}>{t('profile.helpSupport')}</Text>
          </View>
          {isRTL ? <ChevronLeft size={20} color={colors.gray[400]} /> : <ChevronRight size={20} color={colors.gray[400]} />}
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.logoutButton}
          onPress={handleLogout}
        >
          <LogOut size={20} color={colors.danger} />
          <Text style={styles.logoutText}>{t('profile.logout')}</Text>
        </TouchableOpacity>
      </ScrollView>

      {/* Language Selection Modal */}
      <Modal
        visible={showLanguageModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowLanguageModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{t('profile.selectLanguage')}</Text>
              <TouchableOpacity onPress={() => setShowLanguageModal(false)}>
                <Text style={styles.modalCloseText}>{t('profile.close')}</Text>
              </TouchableOpacity>
            </View>
            <FlatList
              data={languages}
              renderItem={renderLanguageItem}
              keyExtractor={item => item.id}
              style={styles.modalList}
            />
          </View>
        </View>
      </Modal>

      {/* Change Password Modal */}
      <Modal
        visible={showPasswordModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowPasswordModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{t('profile.changePassword')}</Text>
              <TouchableOpacity onPress={() => setShowPasswordModal(false)}>
                <Text style={styles.modalCloseText}>{t('common.cancel')}</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.passwordModalContent}>
              <View style={styles.passwordInputContainer}>
                <Text style={styles.passwordInputLabel}>{t('profile.currentPassword')}</Text>
                <View style={styles.passwordInputWrapper}>
                  <TextInput
                    style={styles.passwordInput}
                    value={currentPassword}
                    onChangeText={setCurrentPassword}
                    placeholder={t('profile.currentPassword')}
                    secureTextEntry={!showCurrentPassword}
                    textAlign={isRTL ? 'right' : 'left'}
                  />
                  <TouchableOpacity
                    style={styles.passwordVisibilityButton}
                    onPress={() => setShowCurrentPassword(!showCurrentPassword)}
                  >
                    {showCurrentPassword ? (
                      <EyeOff size={20} color={colors.gray[500]} />
                    ) : (
                      <Eye size={20} color={colors.gray[500]} />
                    )}
                  </TouchableOpacity>
                </View>
              </View>

              <View style={styles.passwordInputContainer}>
                <Text style={styles.passwordInputLabel}>{t('profile.newPassword')}</Text>
                <View style={styles.passwordInputWrapper}>
                  <TextInput
                    style={styles.passwordInput}
                    value={newPassword}
                    onChangeText={setNewPassword}
                    placeholder={t('profile.newPassword')}
                    secureTextEntry={!showNewPassword}
                    textAlign={isRTL ? 'right' : 'left'}
                  />
                  <TouchableOpacity
                    style={styles.passwordVisibilityButton}
                    onPress={() => setShowNewPassword(!showNewPassword)}
                  >
                    {showNewPassword ? (
                      <EyeOff size={20} color={colors.gray[500]} />
                    ) : (
                      <Eye size={20} color={colors.gray[500]} />
                    )}
                  </TouchableOpacity>
                </View>
              </View>

              <View style={styles.passwordInputContainer}>
                <Text style={styles.passwordInputLabel}>{t('profile.confirmNewPassword')}</Text>
                <View style={styles.passwordInputWrapper}>
                  <TextInput
                    style={styles.passwordInput}
                    value={confirmPassword}
                    onChangeText={setConfirmPassword}
                    placeholder={t('profile.confirmNewPassword')}
                    secureTextEntry={!showConfirmPassword}
                    textAlign={isRTL ? 'right' : 'left'}
                  />
                  <TouchableOpacity
                    style={styles.passwordVisibilityButton}
                    onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeOff size={20} color={colors.gray[500]} />
                    ) : (
                      <Eye size={20} color={colors.gray[500]} />
                    )}
                  </TouchableOpacity>
                </View>
              </View>

              {passwordError ? (
                <Text style={styles.passwordErrorText}>{passwordError}</Text>
              ) : null}

              <View style={styles.passwordRequirements}>
                <Text style={styles.passwordRequirementsTitle}>{t('profile.passwordRequirements')}</Text>
                <View style={styles.passwordRequirementItem}>
                  <View style={[
                    styles.passwordRequirementDot,
                    newPassword.length >= 8 ? styles.passwordRequirementDotCompleted : null
                  ]} />
                  <Text style={styles.passwordRequirementText}>{t('profile.atLeast8Chars')}</Text>
                </View>
                <View style={styles.passwordRequirementItem}>
                  <View style={[
                    styles.passwordRequirementDot,
                    /\d/.test(newPassword) ? styles.passwordRequirementDotCompleted : null
                  ]} />
                  <Text style={styles.passwordRequirementText}>{t('profile.containsNumber')}</Text>
                </View>
                <View style={styles.passwordRequirementItem}>
                  <View style={[
                    styles.passwordRequirementDot,
                    /[!@#$%^&*(),.?":{}|<>]/.test(newPassword) ? styles.passwordRequirementDotCompleted : null
                  ]} />
                  <Text style={styles.passwordRequirementText}>{t('profile.containsSpecialChar')}</Text>
                </View>
              </View>

              <TouchableOpacity
                style={styles.changePasswordButton}
                onPress={handleChangePassword}
                disabled={isChangingPassword}
              >
                {isChangingPassword ? (
                  <ActivityIndicator size="small" color={colors.white} />
                ) : (
                  <Text style={styles.changePasswordButtonText}>{t('profile.changePassword')}</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  profileImageContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  profileImagePlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  editButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: colors.white,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  editButtonText: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '500',
  },
  userName: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.gray[800],
    marginBottom: 4,
    textAlign: 'center',
  },
  userEmail: {
    fontSize: 14,
    color: colors.gray[500],
    marginBottom: 8,
    textAlign: 'center',
  },
  roleBadge: {
    backgroundColor: colors.primaryLight,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  roleText: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '500',
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.gray[800],
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.gray[500],
    textAlign: 'center',
  },
  statDivider: {
    width: 1,
    height: '80%',
    backgroundColor: colors.gray[200],
    alignSelf: 'center',
  },
  sectionHeader: {
    marginBottom: 12,
    marginTop: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  activityItem: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  activityIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: I18nManager.isRTL ? 0 : 12,
    marginLeft: I18nManager.isRTL ? 12 : 0,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 14,
    color: colors.gray[800],
    marginBottom: 4,
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  activityTime: {
    fontSize: 12,
    color: colors.gray[500],
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  emptyActivity: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    alignItems: 'center',
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  emptyActivityText: {
    fontSize: 14,
    color: colors.gray[500],
  },
  settingItem: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: I18nManager.isRTL ? 0 : 12,
    marginLeft: I18nManager.isRTL ? 12 : 0,
  },
  settingContent: {
    flex: 1,
  },
  settingText: {
    fontSize: 14,
    color: colors.gray[800],
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  settingSubtext: {
    fontSize: 12,
    color: colors.gray[500],
    marginTop: 2,
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginTop: 12,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  logoutText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.danger,
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  modalCloseText: {
    fontSize: 16,
    color: colors.primary,
  },
  modalList: {
    maxHeight: '80%',
  },
  modalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalItemIcon: {
    marginRight: 12,
  },
  modalItemText: {
    fontSize: 16,
    color: colors.gray[800],
  },
  modalItemSubtext: {
    fontSize: 14,
    color: colors.gray[500],
  },
  passwordModalContent: {
    padding: 16,
  },
  passwordInputContainer: {
    marginBottom: 16,
  },
  passwordInputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  passwordInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    backgroundColor: colors.white,
  },
  passwordInput: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    fontSize: 14,
    color: colors.gray[800],
  },
  passwordVisibilityButton: {
    padding: 12,
  },
  passwordErrorText: {
    color: colors.danger,
    fontSize: 14,
    marginBottom: 16,
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  passwordRequirements: {
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  passwordRequirementsTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  passwordRequirementItem: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  passwordRequirementDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.gray[300],
    marginRight: I18nManager.isRTL ? 0 : 8,
    marginLeft: I18nManager.isRTL ? 8 : 0,
  },
  passwordRequirementDotCompleted: {
    backgroundColor: colors.success,
  },
  passwordRequirementText: {
    fontSize: 14,
    color: colors.gray[600],
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  changePasswordButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  changePasswordButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
});
