import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import { router } from 'expo-router';
import { useFarmStore } from '@/store/farm-store';
import { colors } from '@/constants/colors';
import { StyleSheet } from 'react-native';

export default function DebugTestComponent() {
  const [testResults, setTestResults] = useState<string[]>([]);
  const { gardens, currentFarm, inventoryEquipment } = useFarmStore();

  const runTests = () => {
    const results: string[] = [];

    // Test 1: Check if gardens have location data
    const gardensWithLocation = gardens.filter(g => g.location);
    results.push(`Gardens with location: ${gardensWithLocation.length}/${gardens.length}`);

    // Test 2: Check if equipment is accessible
    try {
      const equipmentCount = inventoryEquipment.length;
      results.push(`Equipment accessible: ✅ (${equipmentCount} items)`);
    } catch (error) {
      results.push(`Equipment error: ❌ ${error}`);
    }

    // Test 3: Check current farm
    results.push(`Current farm: ${currentFarm ? '✅' : '❌'} (${currentFarm?.name || 'None'})`);

    setTestResults(results);
    
    Alert.alert(
      'Debug Test Results',
      results.join('\n'),
      [{ text: 'OK' }]
    );
  };

  const testGardenNavigation = () => {
    if (gardens.length > 0) {
      const firstGarden = gardens[0];
      router.push(`/garden/${firstGarden.id}`);
    } else {
      Alert.alert('No Gardens', 'No gardens available to test');
    }
  };

  const testMapsNavigation = () => {
    router.push('/(tabs)/map');
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Debug Test Panel</Text>
      
      <TouchableOpacity style={styles.button} onPress={runTests}>
        <Text style={styles.buttonText}>Run Debug Tests</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={testGardenNavigation}>
        <Text style={styles.buttonText}>Test Garden Details</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={testMapsNavigation}>
        <Text style={styles.buttonText}>Test Maps Screen</Text>
      </TouchableOpacity>

      {testResults.length > 0 && (
        <View style={styles.resultsContainer}>
          <Text style={styles.resultsTitle}>Test Results:</Text>
          {testResults.map((result, index) => (
            <Text key={index} style={styles.resultText}>
              {result}
            </Text>
          ))}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: colors.white,
    borderRadius: 8,
    margin: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.gray[800],
    marginBottom: 16,
    textAlign: 'center',
  },
  button: {
    backgroundColor: colors.primary,
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: colors.white,
    fontSize: 14,
    fontWeight: '600',
  },
  resultsContainer: {
    marginTop: 16,
    padding: 12,
    backgroundColor: colors.gray[50],
    borderRadius: 8,
  },
  resultsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 8,
  },
  resultText: {
    fontSize: 12,
    color: colors.gray[600],
    marginBottom: 4,
    fontFamily: 'monospace',
  },
});
