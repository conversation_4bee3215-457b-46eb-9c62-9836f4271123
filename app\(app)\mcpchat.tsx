import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  Image,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { ChatMessage, MCPSuggestedAction } from '@/services/ai-assistant';
import { mcpServerService } from '@/services/mcp-server';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import { colors } from '@/constants/colors';
import { StyleSheet } from 'react-native';
import { v4 as uuidv4 } from 'uuid';
import MCPMessageComponent from '@/components/mcp/MCPMessageComponent';
import MCPActionHandler from '@/components/mcp/MCPActionHandler';
import { useTranslation } from '@/i18n/useTranslation';

export default function MCPChatScreen() {
  const { isRTL } = useTranslation()
  
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showActionHandler, setShowActionHandler] = useState(false);
  const [pendingActions, setPendingActions] = useState<MCPSuggestedAction[]>([]);
  
  const flatListRef = useRef<FlatList>(null);
  
  const { currentFarm, animals, plants, gardens, fields, equipment } = useFarmStore();
  const { user } = useAuthStore();

  useEffect(() => {
    // Scroll to bottom when new messages are added
    if (messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]);

  const handleImagePicker = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsMultipleSelection: false,
      quality: 0.8,
      base64: false,
    });

    if (!result.canceled) {
      setSelectedImages([result.assets[0].uri]);
    }
  };

  const handleCamera = async () => {
    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ['images'],
      quality: 0.8,
      base64: false,
    });

    if (!result.canceled) {
      setSelectedImages([result.assets[0].uri]);
    }
  };

  const removeImage = (index: number) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index));
  };

  const handleSendMessage = async () => {
    if (!inputText.trim() && selectedImages.length === 0) return;
    if (!currentFarm || !user) {
      Alert.alert('Error', 'No farm or user selected');
      return;
    }

    const userMessage: ChatMessage = {
      id: uuidv4(),
      role: 'user',
      content: inputText,
      images: selectedImages,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setSelectedImages([]);
    setIsLoading(true);

    try {
      const context = {
        farmId: currentFarm.id,
        userId: (user as any)?.id || (user as any)?.uid || '123456',
        language: isRTL ? 'ur' : 'en',
        currentEntities: [
          ...animals,
          ...plants,
          ...gardens,
          ...fields,
          ...equipment,
        ],
      };

      const result = await mcpServerService.analyzeMessage({
        message: inputText,
        images: selectedImages,
        context,
      });

      const assistantMessage: ChatMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: result.summary,
        timestamp: new Date(),
        metadata: {
          summary: result.summary,
          entityData: result.entityData,
          suggestedActionArray: result.suggestedActionArray,
        },
      };

      setMessages(prev => [...prev, assistantMessage]);

      if (result.suggestedActionArray.length > 0) {
        setPendingActions(result.suggestedActionArray);
        setShowActionHandler(true);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: ChatMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: 'Sorry, I encountered an error processing your request. Please try again.',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const renderMessage = ({ item }: { item: ChatMessage }) => (
    <MCPMessageComponent
      message={item}
      onActionPress={(actions) => {
        setPendingActions(actions);
        setShowActionHandler(true);
      }}
      onImagePress={(imageUrl) => {
        console.log('Image pressed:', imageUrl);
      }}
    />
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>MCP AI Assistant</Text>
      </View>
      
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={item => item.id}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContent}
      />

      {isLoading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={colors.primary} />
          <Text style={styles.loadingText}>AI is thinking...</Text>
        </View>
      )}

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.inputSection}
      >
        {selectedImages.length > 0 && (
          <View style={styles.selectedImagesContainer}>
            {selectedImages.map((uri, index) => (
              <View key={index} style={styles.selectedImageContainer}>
                <Image source={{ uri }} style={styles.selectedImage} />
                <TouchableOpacity
                  style={styles.removeImageButton}
                  onPress={() => removeImage(index)}
                >
                  <Ionicons name="close-circle" size={20} color={colors.danger} />
                </TouchableOpacity>
              </View>
            ))}
          </View>
        )}

        <View style={styles.inputContainer}>
          <TouchableOpacity style={styles.imageButton} onPress={handleCamera}>
            <Ionicons name="camera" size={24} color={colors.primary} />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.imageButton} onPress={handleImagePicker}>
            <Ionicons name="image" size={24} color={colors.primary} />
          </TouchableOpacity>

          <TextInput
            style={styles.textInput}
            value={inputText}
            onChangeText={setInputText}
            placeholder="Ask me anything about your farm..."
            multiline
            maxLength={1000}
          />

          <TouchableOpacity
            style={[
              styles.sendButton,
              (!inputText.trim() && selectedImages.length === 0) && styles.sendButtonDisabled
            ]}
            onPress={handleSendMessage}
            disabled={!inputText.trim() && selectedImages.length === 0}
          >
            <Ionicons name="send" size={20} color={colors.white} />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>

      {showActionHandler && pendingActions.length > 0 && (
        <View style={styles.actionHandlerContainer}>
          <MCPActionHandler
            actions={pendingActions}
            onActionComplete={(action, result) => {
              console.log('MCP Action completed:', action.title, result);
            }}
            onAllActionsComplete={() => {
              setShowActionHandler(false);
              setPendingActions([]);
            }}
          />

          <TouchableOpacity
            style={styles.closeActionHandlerButton}
            onPress={() => setShowActionHandler(false)}
          >
            <Text style={styles.closeActionHandlerText}>Close</Text>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  header: {
    backgroundColor: colors.primary,
    padding: 16,
    alignItems: 'center',
  },
  headerTitle: {
    color: colors.white,
    fontSize: 18,
    fontWeight: '600',
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    padding: 16,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  loadingText: {
    marginLeft: 8,
    color: colors.gray[600],
    fontSize: 14,
  },
  inputSection: {
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  selectedImagesContainer: {
    flexDirection: 'row',
    padding: 12,
    gap: 8,
  },
  selectedImageContainer: {
    position: 'relative',
  },
  selectedImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: colors.white,
    borderRadius: 10,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    padding: 12,
    gap: 8,
  },
  imageButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: colors.gray[100],
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    maxHeight: 100,
    fontSize: 16,
  },
  sendButton: {
    backgroundColor: colors.primary,
    borderRadius: 20,
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: colors.gray[300],
  },
  actionHandlerContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    padding: 20,
  },
  closeActionHandlerButton: {
    backgroundColor: colors.gray[600],
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  closeActionHandlerText: {
    color: colors.white,
    fontSize: 14,
    fontWeight: '600',
  },
});
