import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { colors } from '@/constants/colors';
import { Database } from 'lucide-react-native';

interface MCPEntityDataDisplayProps {
  entityData: any;
  isRTL: boolean;
}

export default function MCPEntityDataDisplay({ entityData, isRTL }: MCPEntityDataDisplayProps) {
  if (!entityData || typeof entityData !== 'object') {
    return null;
  }

  const renderDataItem = (key: string, value: any) => {
    if (value === null || value === undefined || value === '') {
      return null;
    }

    const displayValue = typeof value === 'object' ? JSON.stringify(value) : String(value);

    return (
      <View key={key} style={[styles.dataItem, isRTL && styles.rtlDataItem]}>
        <Text style={[styles.dataKey, isRTL && styles.rtlText]}>
          {key.charAt(0).toUpperCase() + key.slice(1)}:
        </Text>
        <Text style={[styles.dataValue, isRTL && styles.rtlText]}>
          {displayValue}
        </Text>
      </View>
    );
  };

  return (
    <View style={[styles.container, isRTL && styles.rtlContainer]}>
      <View style={[styles.header, isRTL && styles.rtlHeader]}>
        <Database size={16} color={colors.success} />
        <Text style={[styles.headerText, isRTL && styles.rtlText]}>
          Entity Details
        </Text>
      </View>
      
      <View style={styles.dataContainer}>
        {Object.entries(entityData).map(([key, value]) => 
          renderDataItem(key, value)
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.green[50],
    borderLeftWidth: 3,
    borderLeftColor: colors.success,
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  rtlContainer: {
    borderLeftWidth: 0,
    borderRightWidth: 3,
    borderRightColor: colors.success,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  rtlHeader: {
    flexDirection: 'row-reverse',
  },
  headerText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.success,
    marginLeft: 6,
  },
  rtlText: {
    textAlign: 'right',
    marginLeft: 0,
    marginRight: 6,
  },
  dataContainer: {
    gap: 8,
  },
  dataItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  rtlDataItem: {
    flexDirection: 'row-reverse',
  },
  dataKey: {
    fontSize: 13,
    fontWeight: '600',
    color: colors.gray[700],
    minWidth: 80,
    marginRight: 8,
  },
  dataValue: {
    fontSize: 13,
    color: colors.gray[600],
    flex: 1,
    lineHeight: 18,
  },
});
