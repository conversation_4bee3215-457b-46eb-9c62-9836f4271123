import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  FlatList,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { MCPSuggestedAction } from '@/services/ai-assistant';
import { mcpServerService } from '@/services/mcp-server';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import { colors } from '@/constants/colors';
import { StyleSheet } from 'react-native';

interface MCPActionModalProps {
  visible: boolean;
  actions: MCPSuggestedAction[];
  onClose: () => void;
  onConfirm: (confirmedActions: MCPSuggestedAction[]) => void;
}

export default function MCPActionModal({
  visible,
  actions,
  onClose,
  onConfirm,
}: MCPActionModalProps) {
  const [completedActions, setCompletedActions] = useState<MCPSuggestedAction[]>([]);
  const [executingActions, setExecutingActions] = useState<Set<string>>(new Set());

  const { currentFarm } = useFarmStore();
  const { user } = useAuthStore();

  const executeAction = async (action: MCPSuggestedAction) => {
    if (!currentFarm || !user) {
      Alert.alert('Error', 'No farm or user selected');
      return;
    }

    setExecutingActions(prev => new Set(prev).add(action.id));

    try {
      const context = {
        farmId: currentFarm.id,
        userId: user.uid,
      };

      const result = await mcpServerService.executeAction(action, context);
      
      // Mark action as completed
      setCompletedActions(prev => [...prev, action]);
      
      Alert.alert('Success', `Action "${action.title}" completed successfully!`);
      console.log('Action result:', result);
    } catch (error) {
      console.error('Error executing action:', error);
      Alert.alert('Error', `Failed to execute action: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setExecutingActions(prev => {
        const newSet = new Set(prev);
        newSet.delete(action.id);
        return newSet;
      });
    }
  };

  const handleFinish = () => {
    onConfirm(completedActions);
    setCompletedActions([]);
    setExecutingActions(new Set());
  };

  const getActionIcon = (action: MCPSuggestedAction) => {
    const iconMap = {
      animal: 'paw',
      plant: 'leaf',
      garden: 'flower',
      field: 'grid',
      equipment: 'construct',
      crop: 'nutrition',
      task: 'checkmark-circle',
    };
    return iconMap[action.entity] || 'help';
  };

  const getActionColor = (action: MCPSuggestedAction) => {
    const colorMap = {
      save: colors.success,
      get: colors.primary,
      update: colors.warning,
      delete: colors.danger,
    };
    return colorMap[action.type];
  };

  const getActionTypeIcon = (type: string) => {
    const iconMap = {
      save: 'save',
      get: 'search',
      update: 'create',
      delete: 'trash',
    };
    return iconMap[type] || 'help';
  };

  const isActionCompleted = (actionId: string) => {
    return completedActions.some(action => action.id === actionId);
  };

  const isActionExecuting = (actionId: string) => {
    return executingActions.has(actionId);
  };

  const renderAction = ({ item }: { item: MCPSuggestedAction }) => {
    const isCompleted = isActionCompleted(item.id);
    const isExecuting = isActionExecuting(item.id);
    
    return (
      <TouchableOpacity
        style={[
          styles.actionItem,
          isCompleted && styles.actionItemCompleted,
          isExecuting && styles.actionItemExecuting,
        ]}
        onPress={() => !isCompleted && !isExecuting && executeAction(item)}
        disabled={isCompleted || isExecuting}
      >
        <View style={styles.actionHeader}>
          <View style={styles.actionIconContainer}>
            <Ionicons
              name={isCompleted ? 'checkmark-circle' : getActionIcon(item) as any}
              size={20}
              color={isCompleted ? colors.success : getActionColor(item)}
            />
          </View>

          <View style={styles.actionInfo}>
            <View style={styles.actionTitleRow}>
              <Text style={styles.actionTitle}>{item.title}</Text>
              <View style={styles.actionTypeContainer}>
                <Ionicons
                  name={getActionTypeIcon(item.type) as any}
                  size={14}
                  color={getActionColor(item)}
                />
                <Text style={[styles.actionType, { color: getActionColor(item) }]}>
                  {item.type.toUpperCase()}
                </Text>
              </View>
            </View>
            
            <Text style={styles.actionDescription}>{item.description}</Text>
            
            {item.confidence && (
              <Text style={styles.actionMeta}>
                {item.entity.toUpperCase()} • {Math.round(item.confidence * 100)}% confidence
              </Text>
            )}

            {/* Show data preview for save/update actions */}
            {(item.type === 'save' || item.type === 'update') && item.data && (
              <View style={styles.dataPreview}>
                <Text style={styles.dataPreviewTitle}>Entity Data:</Text>
                <Text style={styles.dataPreviewText} numberOfLines={3}>
                  {typeof item.data === 'object' ?
                    Object.entries(item.data).map(([key, value]) => `${key}: ${value}`).join('\n') :
                    String(item.data)
                  }
                </Text>
              </View>
            )}

            {/* Show query preview for get actions */}
            {item.type === 'get' && item.query && (
              <View style={styles.dataPreview}>
                <Text style={styles.dataPreviewTitle}>Query:</Text>
                <Text style={styles.dataPreviewText} numberOfLines={2}>
                  {JSON.stringify(item.query, null, 2)}
                </Text>
              </View>
            )}
          </View>

          <View style={styles.actionStatus}>
            {isExecuting ? (
              <ActivityIndicator size="small" color={colors.primary} />
            ) : isCompleted ? (
              <Text style={styles.completedText}>✓ Done</Text>
            ) : (
              <Ionicons name="chevron-forward" size={20} color={colors.gray[500]} />
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>MCP AI Actions</Text>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color={colors.gray[800]} />
          </TouchableOpacity>
        </View>

        <Text style={styles.subtitle}>
          Tap on each action to execute it. The MCP server will handle the operation:
        </Text>

        <FlatList
          data={actions}
          renderItem={renderAction}
          keyExtractor={item => item.id}
          style={styles.actionsList}
        />

        <View style={styles.footer}>
          <TouchableOpacity style={styles.finishButton} onPress={handleFinish}>
            <Text style={styles.finishButtonText}>
              Finish ({completedActions.length}/{actions.length} completed)
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.gray[800],
  },
  subtitle: {
    fontSize: 14,
    color: colors.gray[600],
    padding: 20,
    paddingTop: 10,
  },
  actionsList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  actionItem: {
    backgroundColor: colors.white,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: colors.gray[200],
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  actionItemCompleted: {
    backgroundColor: colors.success + '10',
    borderColor: colors.success,
  },
  actionItemExecuting: {
    backgroundColor: colors.primary + '10',
    borderColor: colors.primary,
  },
  actionHeader: {
    flexDirection: 'row',
    padding: 16,
  },
  actionIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  actionInfo: {
    flex: 1,
  },
  actionTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    flex: 1,
    marginRight: 8,
  },
  actionTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[100],
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  actionType: {
    fontSize: 10,
    fontWeight: '600',
    marginLeft: 4,
  },
  actionDescription: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 8,
    lineHeight: 20,
  },
  actionMeta: {
    fontSize: 12,
    color: colors.gray[500],
    marginBottom: 8,
  },
  dataPreview: {
    backgroundColor: colors.gray[50],
    padding: 8,
    borderRadius: 6,
    marginTop: 4,
  },
  dataPreviewTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.gray[700],
    marginBottom: 4,
  },
  dataPreviewText: {
    fontSize: 11,
    color: colors.gray[600],
    fontFamily: 'monospace',
  },
  actionStatus: {
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  completedText: {
    fontSize: 12,
    color: colors.success,
    fontWeight: '600',
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  finishButton: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  finishButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});
