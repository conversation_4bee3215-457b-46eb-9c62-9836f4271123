import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDocs,
  getDoc,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
} from 'firebase/firestore';
import { firestore } from '@/firebase/config';
import { MCPSuggestedAction } from './ai-assistant';

export interface MCPFirestoreContext {
  farmId: string;
  userId: string;
}

export class MCPFirestoreOperations {
  /**
   * Execute a save action - creates a new document
   */
  static async executeSaveAction(
    action: MCPSuggestedAction,
    context: MCPFirestoreContext
  ): Promise<any> {
    if (!action.data) {
      throw new Error('Save action requires data');
    }

    const { farmId } = context;
    const collectionName = this.getCollectionName(action.entity);

    // Transform MCP entity data to Firestore format
    const transformedData = this.transformEntityData(action.entity, action.data);

    const docData = {
      ...transformedData,
      farmId,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    };

    try {
      const docRef = await addDoc(
        collection(firestore, `farms/${farmId}/${collectionName}`),
        docData
      );

      return {
        id: docRef.id,
        ...docData,
        success: true,
        message: `${action.entity} created successfully`,
      };
    } catch (error) {
      console.error('Error executing save action:', error);
      throw new Error(`Failed to save ${action.entity}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Execute a get action - retrieves documents based on query
   */
  static async executeGetAction(
    action: MCPSuggestedAction,
    context: MCPFirestoreContext
  ): Promise<any> {
    const { farmId } = context;
    const collectionName = this.getCollectionName(action.entity);
    
    try {
      let q = collection(firestore, `farms/${farmId}/${collectionName}`);
      
      // Apply query filters if provided
      if (action.query) {
        const { where: whereClause, orderBy: orderByClause, limit: limitClause } = action.query;
        
        if (whereClause) {
          if (Array.isArray(whereClause)) {
            // Multiple where clauses
            whereClause.forEach(([field, operator, value]) => {
              q = query(q, where(field, operator, value));
            });
          } else {
            // Single where clause
            const [field, operator, value] = whereClause;
            q = query(q, where(field, operator, value));
          }
        }
        
        if (orderByClause) {
          const [field, direction = 'asc'] = orderByClause;
          q = query(q, orderBy(field, direction));
        }
        
        if (limitClause) {
          q = query(q, limit(limitClause));
        }
      }

      const snapshot = await getDocs(q);
      const documents = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      }));

      return {
        data: documents,
        count: documents.length,
        success: true,
        message: `Retrieved ${documents.length} ${action.entity}(s)`,
      };
    } catch (error) {
      console.error('Error executing get action:', error);
      throw new Error(`Failed to get ${action.entity}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Execute an update action - updates an existing document
   */
  static async executeUpdateAction(
    action: MCPSuggestedAction,
    context: MCPFirestoreContext
  ): Promise<any> {
    if (!action.data || !action.data.id) {
      throw new Error('Update action requires data with id');
    }

    const { farmId } = context;
    const collectionName = this.getCollectionName(action.entity);
    const { id, ...updateData } = action.data;
    
    const docData = {
      ...updateData,
      updatedAt: serverTimestamp(),
    };

    try {
      const docRef = doc(firestore, `farms/${farmId}/${collectionName}`, id);
      await updateDoc(docRef, docData);

      return {
        id,
        ...docData,
        success: true,
        message: `${action.entity} updated successfully`,
      };
    } catch (error) {
      console.error('Error executing update action:', error);
      throw new Error(`Failed to update ${action.entity}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Execute a delete action - deletes a document
   */
  static async executeDeleteAction(
    action: MCPSuggestedAction,
    context: MCPFirestoreContext
  ): Promise<any> {
    if (!action.data || !action.data.id) {
      throw new Error('Delete action requires data with id');
    }

    const { farmId } = context;
    const collectionName = this.getCollectionName(action.entity);
    const { id } = action.data;

    try {
      const docRef = doc(firestore, `farms/${farmId}/${collectionName}`, id);
      await deleteDoc(docRef);

      return {
        id,
        success: true,
        message: `${action.entity} deleted successfully`,
      };
    } catch (error) {
      console.error('Error executing delete action:', error);
      throw new Error(`Failed to delete ${action.entity}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get the Firestore collection name for an entity type
   */
  private static getCollectionName(entity: string): string {
    const collectionMap: { [key: string]: string } = {
      animal: 'animals',
      plant: 'plants',
      garden: 'gardens',
      field: 'fields',
      equipment: 'equipment',
      crop: 'crops',
      task: 'tasks',
    };

    return collectionMap[entity] || entity;
  }

  /**
   * Execute any MCP action based on its type
   */
  static async executeAction(
    action: MCPSuggestedAction,
    context: MCPFirestoreContext
  ): Promise<any> {
    switch (action.type) {
      case 'save':
        return this.executeSaveAction(action, context);
      
      case 'get':
        return this.executeGetAction(action, context);
      
      case 'update':
        return this.executeUpdateAction(action, context);
      
      case 'delete':
        return this.executeDeleteAction(action, context);
      
      default:
        throw new Error(`Unsupported action type: ${action.type}`);
    }
  }

  /**
   * Validate action data before execution
   */
  static validateAction(action: MCPSuggestedAction): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!action.id) {
      errors.push('Action ID is required');
    }

    if (!action.type) {
      errors.push('Action type is required');
    }

    if (!action.entity) {
      errors.push('Action entity is required');
    }

    if (!action.title) {
      errors.push('Action title is required');
    }

    // Type-specific validations
    switch (action.type) {
      case 'save':
        if (!action.data) {
          errors.push('Save action requires data');
        }
        break;
      
      case 'update':
      case 'delete':
        if (!action.data || !action.data.id) {
          errors.push(`${action.type} action requires data with id`);
        }
        break;
      
      case 'get':
        // Get actions can work without data or query, but query is recommended
        break;
      
      default:
        errors.push(`Unsupported action type: ${action.type}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Transform MCP entity data to Firestore format
   */
  private static transformEntityData(entity: string, data: any): any {
    // Handle different entity types and transform data accordingly
    switch (entity) {
      case 'plant':
        return {
          name: data.name || 'Unknown Plant',
          species: data.species || 'Unknown Species',
          variety: data.variety || 'Unknown',
          status: data.status || 'active',
          healthStatus: data.healthStatus || 'healthy',
          plantedDate: data.plantingDate ? new Date(data.plantingDate) : new Date(),
          notes: `Added via MCP AI Assistant`,
        };

      case 'animal':
        return {
          name: data.name || 'Unknown Animal',
          species: data.species || 'Unknown Species',
          breed: data.breed || 'Unknown',
          status: data.status || 'active',
          healthStatus: data.healthStatus || 'healthy',
          dateOfBirth: data.dateOfBirth ? new Date(data.dateOfBirth) : null,
          notes: `Added via MCP AI Assistant`,
        };

      case 'equipment':
        return {
          name: data.name || 'Unknown Equipment',
          type: data.type || 'Unknown',
          category: data.category || 'Equipment',
          status: data.status || 'active',
          manufacturer: data.manufacturer || 'Unknown',
          model: data.model || 'Unknown',
          notes: `Added via MCP AI Assistant`,
        };

      default:
        // For other entities, use data as-is with some defaults
        return {
          name: data.name || `Unknown ${entity}`,
          status: data.status || 'active',
          notes: `Added via MCP AI Assistant`,
          ...data,
        };
    }
  }

  /**
   * Get entity by ID
   */
  static async getEntityById(
    entity: string,
    id: string,
    farmId: string
  ): Promise<any> {
    try {
      const collectionName = this.getCollectionName(entity);
      const docRef = doc(firestore, `farms/${farmId}/${collectionName}`, id);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data(),
        };
      } else {
        throw new Error(`${entity} with id ${id} not found`);
      }
    } catch (error) {
      console.error('Error getting entity by ID:', error);
      throw error;
    }
  }
}
